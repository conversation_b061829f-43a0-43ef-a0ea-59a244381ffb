<template>
  <view class="demo-page">
    <responsive-wrapper :center="true" :shadow="true" :rounded="true" max-width="lg">
      <view class="demo-header">
        <text class="demo-title">响应式布局演示</text>
        <text class="demo-subtitle">Desktop & Mobile Responsive Design</text>
      </view>
    </responsive-wrapper>
    
    <responsive-wrapper :center="true" :shadow="true" :rounded="true" max-width="lg" custom-class="margin-top">
      <view class="demo-content">
        <view class="demo-section">
          <text class="section-title">屏幕信息</text>
          <view class="info-grid">
            <view class="info-item">
              <text class="info-label">屏幕宽度:</text>
              <text class="info-value">{{ screenInfo.width }}px</text>
            </view>
            <view class="info-item">
              <text class="info-label">屏幕高度:</text>
              <text class="info-value">{{ screenInfo.height }}px</text>
            </view>
            <view class="info-item">
              <text class="info-label">设备类型:</text>
              <text class="info-value">{{ isDesktop ? '桌面端' : '移动端' }}</text>
            </view>
          </view>
        </view>
        
        <view class="demo-section">
          <text class="section-title">响应式网格</text>
          <view class="responsive-grid" :class="responsiveGridClass">
            <view class="grid-item" v-for="n in 6" :key="n" :class="responsiveCardClass">
              <text class="grid-text">项目 {{ n }}</text>
            </view>
          </view>
        </view>
        
        <view class="demo-section">
          <text class="section-title">响应式按钮</text>
          <view class="button-group">
            <button class="demo-button" :class="responsiveButtonClass">
              {{ responsiveButtonText }}
            </button>
            <button class="demo-button secondary" :class="responsiveCardClass">
              响应式悬停效果
            </button>
          </view>
        </view>
      </view>
    </responsive-wrapper>
  </view>
</template>

<script>
export default {
  name: 'ResponsiveDemo',
  data() {
    return {
      // 继承自响应式混入的数据会自动可用
    };
  },

  computed: {
    responsiveButtonClass() {
      return this.isDesktop ? 'desktop-btn' : 'mobile-btn';
    },
    responsiveButtonText() {
      return this.isDesktop ? '桌面端按钮' : '移动端按钮';
    }
  },

  onLoad() {
    console.log('响应式演示页面加载');
    console.log('当前屏幕信息:', this.screenInfo);
    console.log('是否为桌面端:', this.isDesktop);
  },

  methods: {
    // 可以使用响应式混入提供的方法
  }
};
</script>

<style scoped>
.demo-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}

.margin-top {
  margin-top: 20rpx;
}

.demo-header {
  padding: 40rpx;
  text-align: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.demo-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.demo-subtitle {
  display: block;
  font-size: 24rpx;
  opacity: 0.8;
}

.demo-content {
  padding: 40rpx;
}

.demo-section {
  margin-bottom: 40rpx;
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 2rpx solid #e0e0e0;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200rpx, 1fr));
  gap: 20rpx;
}

.info-item {
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.info-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.info-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.responsive-grid {
  display: grid;
  gap: 20rpx;
}

.mobile-flex {
  display: flex !important;
  flex-wrap: wrap;
}

.mobile-flex .grid-item {
  flex: 1;
  min-width: 150rpx;
}

.desktop-grid {
  grid-template-columns: repeat(3, 1fr);
}

.grid-item {
  padding: 30rpx;
  background-color: #ffffff;
  border-radius: 8rpx;
  text-align: center;
  border: 2rpx solid #e0e0e0;
  transition: all 0.3s ease;
}

.grid-text {
  font-size: 28rpx;
  color: #333;
}

.button-group {
  display: flex;
  gap: 20rpx;
  flex-wrap: wrap;
}

.demo-button {
  padding: 20rpx 40rpx;
  border: none;
  border-radius: 8rpx;
  background-color: #007bff;
  color: white;
  font-size: 28rpx;
  transition: all 0.3s ease;
}

.demo-button.secondary {
  background-color: #6c757d;
}

.mobile-btn {
  font-size: 26rpx;
  padding: 15rpx 30rpx;
}

.desktop-btn {
  font-size: 32rpx;
  padding: 25rpx 50rpx;
}

/* 桌面端样式 */
@media screen and (min-width: 1024px) {
  .demo-page {
    padding: 40px;
  }
  
  .demo-header {
    padding: 60px;
    border-radius: 12px 12px 0 0;
  }
  
  .demo-title {
    font-size: 28px;
    margin-bottom: 8px;
  }
  
  .demo-subtitle {
    font-size: 16px;
  }
  
  .demo-content {
    padding: 60px;
  }
  
  .section-title {
    font-size: 24px;
    margin-bottom: 16px;
    padding-bottom: 8px;
  }
  
  .info-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
  }
  
  .info-item {
    padding: 20px;
    border-radius: 8px;
  }
  
  .info-label {
    font-size: 14px;
    margin-bottom: 6px;
  }
  
  .info-value {
    font-size: 18px;
  }
  
  .responsive-grid {
    gap: 20px;
  }
  
  .desktop-grid {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .grid-item {
    padding: 30px;
    border-radius: 12px;
  }
  
  .grid-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: #007bff;
  }
  
  .grid-text {
    font-size: 16px;
  }
  
  .demo-button {
    padding: 15px 30px;
    border-radius: 8px;
    font-size: 16px;
  }
  
  .demo-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
  }
  
  .demo-button.secondary:hover {
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
  }
}
</style>
