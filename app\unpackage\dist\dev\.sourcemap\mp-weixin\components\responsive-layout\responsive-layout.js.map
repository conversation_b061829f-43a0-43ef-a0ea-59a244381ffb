{"version": 3, "sources": ["webpack:///D:/桌面/thinker/app/components/responsive-layout/responsive-layout.vue?1343", "webpack:///D:/桌面/thinker/app/components/responsive-layout/responsive-layout.vue?0c78", "webpack:///D:/桌面/thinker/app/components/responsive-layout/responsive-layout.vue?c618", "webpack:///D:/桌面/thinker/app/components/responsive-layout/responsive-layout.vue?36a8", "uni-app:///components/responsive-layout/responsive-layout.vue", "webpack:///D:/桌面/thinker/app/components/responsive-layout/responsive-layout.vue?3055", "webpack:///D:/桌面/thinker/app/components/responsive-layout/responsive-layout.vue?98fa"], "names": ["name", "props", "title", "type", "default", "showHeader", "showSidebar", "showAside", "showFooter", "layoutType", "contentMaxWidth", "centerContent", "customClass", "computed", "layoutClass", "mainClass", "classes", "contentClass"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0I;AAC1I;AACqE;AACL;AACqC;;;AAGrG;AAC8K;AAC9K,gBAAgB,qLAAU;AAC1B,EAAE,uFAAM;AACR,EAAE,wGAAM;AACR,EAAE,iHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAqqB,CAAgB,mqBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCmDzrB;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;EACA;EACAS;IACAC;MACA,QACA,sCACA,kBACA,iBACA;IACA;IACAC;MACA;MAEA;QACAC;MACA;QACAA;MACA;QACAA;MACA;MAEA;IACA;IACAC;MACA,QACA,kBACA,uCACA,kCACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACjIA;AAAA;AAAA;AAAA;AAAs+B,CAAgB,g8BAAG,EAAC,C;;;;;;;;;;;ACA1/B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/responsive-layout/responsive-layout.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./responsive-layout.vue?vue&type=template&id=46ec3b4c&scoped=true&\"\nvar renderjs\nimport script from \"./responsive-layout.vue?vue&type=script&lang=js&\"\nexport * from \"./responsive-layout.vue?vue&type=script&lang=js&\"\nimport style0 from \"./responsive-layout.vue?vue&type=style&index=0&id=46ec3b4c&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"46ec3b4c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/responsive-layout/responsive-layout.vue\"\nexport default component.exports", "export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-layout.vue?vue&type=template&id=46ec3b4c&scoped=true&\"", "var components\ntry {\n  components = {\n    responsiveContainer: function () {\n      return import(\n        /* webpackChunkName: \"components/responsive-container/responsive-container\" */ \"@/components/responsive-container/responsive-container.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-layout.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-layout.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"responsive-layout\" :class=\"layoutClass\">\n    <!-- 顶部导航栏 -->\n    <view class=\"layout-header\" v-if=\"showHeader\">\n      <slot name=\"header\">\n        <view class=\"default-header\">\n          <text class=\"header-title\">{{ title }}</text>\n        </view>\n      </slot>\n    </view>\n    \n    <!-- 主要内容区域 -->\n    <view class=\"layout-main\" :class=\"mainClass\">\n      <!-- 侧边栏 (仅大屏显示) -->\n      <view class=\"layout-sidebar d-xs-none d-sm-none d-md-block\" v-if=\"showSidebar\">\n        <slot name=\"sidebar\">\n          <view class=\"default-sidebar\">\n            <text>侧边栏</text>\n          </view>\n        </slot>\n      </view>\n      \n      <!-- 内容区域 -->\n      <view class=\"layout-content\" :class=\"contentClass\">\n        <responsive-container :maxWidth=\"contentMaxWidth\" :center=\"centerContent\">\n          <slot></slot>\n        </responsive-container>\n      </view>\n      \n      <!-- 右侧栏 (仅超大屏显示) -->\n      <view class=\"layout-aside d-xs-none d-sm-none d-md-none d-lg-none d-xl-block\" v-if=\"showAside\">\n        <slot name=\"aside\">\n          <view class=\"default-aside\">\n            <text>右侧栏</text>\n          </view>\n        </slot>\n      </view>\n    </view>\n    \n    <!-- 底部区域 -->\n    <view class=\"layout-footer\" v-if=\"showFooter\">\n      <slot name=\"footer\">\n        <view class=\"default-footer\">\n          <text class=\"footer-text\">© 2024 贝壳刷题</text>\n        </view>\n      </slot>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'responsive-layout',\n  props: {\n    // 页面标题\n    title: {\n      type: String,\n      default: ''\n    },\n    // 是否显示头部\n    showHeader: {\n      type: Boolean,\n      default: false\n    },\n    // 是否显示侧边栏\n    showSidebar: {\n      type: Boolean,\n      default: false\n    },\n    // 是否显示右侧栏\n    showAside: {\n      type: Boolean,\n      default: false\n    },\n    // 是否显示底部\n    showFooter: {\n      type: Boolean,\n      default: false\n    },\n    // 布局类型: 'default', 'sidebar', 'three-column'\n    layoutType: {\n      type: String,\n      default: 'default'\n    },\n    // 内容最大宽度\n    contentMaxWidth: {\n      type: String,\n      default: '1200px'\n    },\n    // 是否居中内容\n    centerContent: {\n      type: Boolean,\n      default: true\n    },\n    // 自定义类名\n    customClass: {\n      type: String,\n      default: ''\n    }\n  },\n  computed: {\n    layoutClass() {\n      return [\n        'responsive-layout',\n        `layout-${this.layoutType}`,\n        this.customClass\n      ].filter(Boolean).join(' ');\n    },\n    mainClass() {\n      const classes = ['layout-main'];\n      \n      if (this.showSidebar && this.showAside) {\n        classes.push('three-column');\n      } else if (this.showSidebar) {\n        classes.push('with-sidebar');\n      } else if (this.showAside) {\n        classes.push('with-aside');\n      }\n      \n      return classes.join(' ');\n    },\n    contentClass() {\n      return [\n        'layout-content',\n        this.showSidebar ? 'has-sidebar' : '',\n        this.showAside ? 'has-aside' : ''\n      ].filter(Boolean).join(' ');\n    }\n  }\n}\n</script>\n\n<style scoped>\n.responsive-layout {\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background-color: #f5f5f5;\n}\n\n/* 头部样式 */\n.layout-header {\n  background: white;\n  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);\n  z-index: 100;\n}\n\n.default-header {\n  padding: 30rpx;\n  text-align: center;\n}\n\n.header-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n}\n\n/* 主要区域样式 */\n.layout-main {\n  flex: 1;\n  display: flex;\n  min-height: 0;\n}\n\n.layout-main.with-sidebar {\n  /* 有侧边栏的布局 */\n}\n\n.layout-main.with-aside {\n  /* 有右侧栏的布局 */\n}\n\n.layout-main.three-column {\n  /* 三栏布局 */\n}\n\n/* 侧边栏样式 */\n.layout-sidebar {\n  width: 250rpx;\n  background: white;\n  box-shadow: 2rpx 0 8rpx rgba(0,0,0,0.1);\n  z-index: 90;\n}\n\n.default-sidebar {\n  padding: 30rpx;\n  text-align: center;\n  color: #666;\n}\n\n/* 内容区域样式 */\n.layout-content {\n  flex: 1;\n  min-width: 0;\n  padding: 0;\n}\n\n.layout-content.has-sidebar {\n  /* 有侧边栏时的内容样式 */\n}\n\n.layout-content.has-aside {\n  /* 有右侧栏时的内容样式 */\n}\n\n/* 右侧栏样式 */\n.layout-aside {\n  width: 300rpx;\n  background: white;\n  box-shadow: -2rpx 0 8rpx rgba(0,0,0,0.1);\n  z-index: 90;\n}\n\n.default-aside {\n  padding: 30rpx;\n  text-align: center;\n  color: #666;\n}\n\n/* 底部样式 */\n.layout-footer {\n  background: white;\n  border-top: 1rpx solid #e0e0e0;\n  margin-top: auto;\n}\n\n.default-footer {\n  padding: 40rpx;\n  text-align: center;\n}\n\n.footer-text {\n  font-size: 24rpx;\n  color: #999;\n}\n\n/* 响应式适配 */\n@media screen and (min-width: 769rpx) {\n  .layout-sidebar {\n    width: 280rpx;\n  }\n}\n\n@media screen and (min-width: 1025rpx) {\n  .layout-sidebar {\n    width: 320rpx;\n  }\n  \n  .layout-aside {\n    width: 350rpx;\n  }\n  \n  .layout-main.with-sidebar .layout-content,\n  .layout-main.with-aside .layout-content {\n    padding: 0 20rpx;\n  }\n  \n  .layout-main.three-column .layout-content {\n    padding: 0 20rpx;\n  }\n}\n\n@media screen and (min-width: 1441rpx) {\n  .layout-sidebar {\n    width: 380rpx;\n  }\n  \n  .layout-aside {\n    width: 400rpx;\n  }\n}\n</style>\n", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-layout.vue?vue&type=style&index=0&id=46ec3b4c&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-layout.vue?vue&type=style&index=0&id=46ec3b4c&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753560679693\n      var cssReload = require(\"D:/uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}