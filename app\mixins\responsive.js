/**
 * 响应式设计混入
 * 提供响应式相关的计算属性和方法
 */

export default {
  data() {
    return {
      screenInfo: {
        width: 375,
        height: 667,
        isDesktop: false,
        isMobile: true
      }
    };
  },
  
  computed: {
    // 是否为桌面端
    isDesktop() {
      return this.screenInfo.isDesktop;
    },
    
    // 是否为移动端
    isMobile() {
      return this.screenInfo.isMobile;
    },
    
    // 响应式容器类名
    responsiveContainerClass() {
      return {
        'main-container': this.isDesktop,
        'desktop-center': this.isDesktop,
        'desktop-shadow': this.isDesktop,
        'mobile-container': this.isMobile
      };
    },
    
    // 响应式卡片类名
    responsiveCardClass() {
      return {
        'desktop-rounded': this.isDesktop,
        'desktop-shadow': this.isDesktop,
        'desktop-shadow-hover': this.isDesktop
      };
    },
    
    // 响应式网格类名
    responsiveGridClass() {
      return {
        'desktop-grid': this.isDesktop,
        'mobile-flex': this.isMobile
      };
    }
  },
  
  mounted() {
    this.initScreenInfo();
  },
  
  methods: {
    // 初始化屏幕信息
    initScreenInfo() {
      uni.getSystemInfo({
        success: (res) => {
          this.screenInfo.width = res.screenWidth;
          this.screenInfo.height = res.screenHeight;
          this.updateScreenType();
        }
      });
    },
    
    // 更新屏幕类型
    updateScreenType() {
      // 以1024px作为桌面端和移动端的分界点
      const breakpoint = 1024;
      this.screenInfo.isDesktop = this.screenInfo.width >= breakpoint;
      this.screenInfo.isMobile = !this.screenInfo.isDesktop;
    },
    

    
    // 根据屏幕类型获取不同的值
    getResponsiveValue(mobileValue, desktopValue) {
      return this.isDesktop ? desktopValue : mobileValue;
    },
    
    // 根据屏幕类型执行不同的函数
    executeResponsive(mobileFn, desktopFn) {
      if (this.isDesktop && typeof desktopFn === 'function') {
        return desktopFn();
      } else if (this.isMobile && typeof mobileFn === 'function') {
        return mobileFn();
      }
    },
    
    // 获取响应式样式
    getResponsiveStyle(mobileStyle = {}, desktopStyle = {}) {
      return this.isDesktop ? { ...mobileStyle, ...desktopStyle } : mobileStyle;
    },
    
    // 获取响应式类名
    getResponsiveClass(mobileClass = '', desktopClass = '') {
      const classes = [];
      
      if (mobileClass) {
        classes.push(mobileClass);
      }
      
      if (this.isDesktop && desktopClass) {
        classes.push(desktopClass);
      }
      
      return classes.join(' ');
    }
  }
};
