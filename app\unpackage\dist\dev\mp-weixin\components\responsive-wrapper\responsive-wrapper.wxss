
.responsive-wrapper.data-v-4f792484 {
  width: 100%;
  box-sizing: border-box;
}

/* 移动端样式 */
@media screen and (max-width: 1023px) {
.responsive-wrapper.data-v-4f792484 {
    margin: 0;
    max-width: none !important;
    box-shadow: none !important;
    border-radius: 0 !important;
}
}

/* 桌面端样式 */
@media screen and (min-width: 1024px) {
.responsive-center.data-v-4f792484 {
    margin: 0 auto;
}
.max-width-xs.data-v-4f792484 {
    max-width: 600px;
}
.max-width-sm.data-v-4f792484 {
    max-width: 800px;
}
.max-width-md.data-v-4f792484 {
    max-width: 1000px;
}
.max-width-lg.data-v-4f792484 {
    max-width: 1200px;
}
.max-width-xl.data-v-4f792484 {
    max-width: 1400px;
}
.desktop-shadow.data-v-4f792484 {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}
.desktop-rounded.data-v-4f792484 {
    border-radius: 12px;
}
.bg-white.data-v-4f792484 {
    background-color: #ffffff;
}
.bg-transparent.data-v-4f792484 {
    background-color: transparent;
}
}
@media screen and (min-width: 1440px) {
.max-width-lg.data-v-4f792484 {
    max-width: 1400px;
}
.max-width-xl.data-v-4f792484 {
    max-width: 1600px;
}
}

