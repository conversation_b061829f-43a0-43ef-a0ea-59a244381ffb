{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/桌面/thinker/app/App.vue?89c7"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "Polyfill", "init", "<PERSON><PERSON>", "mixin", "Mixin", "ResponsiveMixin", "component", "ResponsiveWrapper", "config", "productionTip", "App", "mpType", "app", "$mount"], "mappings": ";;;;;;;;;;;;;;AAAA;AAE2D;AAE3D;AAGA;AAEA;AAKA;AAAsB;AAAA;AAbtB;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,iBAAQ,CAACC,IAAI,EAAE;AACf;AAAA;EAAA;IAAA;EAAA;AAAA;AAUAC,YAAG,CAACC,KAAK,CAACC,eAAK,CAAC;AAChBF,YAAG,CAACC,KAAK,CAACE,mBAAe,CAAC;AAC1BH,YAAG,CAACI,SAAS,CAAC,mBAAmB,EAAEC,iBAAiB,CAAC;AACrDL,YAAG,CAACM,MAAM,CAACC,aAAa,GAAG,KAAK;AAChCC,YAAG,CAACC,MAAM,GAAG,KAAK;AAClB,IAAMC,GAAG,GAAG,IAAIV,YAAG,mBACZQ,YAAG,EACR;AACF,UAAAE,GAAG,EAACC,MAAM,EAAE,C;;;;;;;;;;;;;ACxBZ;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACsD;AACL;AACc;;;AAG/D;AACwK;AACxK,gBAAgB,qLAAU;AAC1B,EAAE,wEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACe,gF", "file": "common/main.js", "sourcesContent": ["import 'uni-pages';\r\n// @ts-ignore\r\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import App from './App.vue';\r\n// Api函数polyfill（目前为实验版本，如不需要，可删除！）';\r\nimport Polyfill from './polyfill/polyfill.js';\r\nPolyfill.init();\r\n// 全局mixins，用于实现setData等功能，请勿删除！';\r\nimport Mixin from './polyfill/mixins.js';\r\n// 响应式设计混入\r\nimport ResponsiveMixin from './mixins/responsive.js';\r\n// 响应式包装组件\r\nimport ResponsiveWrapper from './components/responsive-wrapper/responsive-wrapper.vue';\r\n\r\n\r\nimport Vue from 'vue';\r\n\r\nVue.mixin(Mixin);\r\nVue.mixin(ResponsiveMixin);\r\nVue.component('ResponsiveWrapper', ResponsiveWrapper);\r\nVue.config.productionTip = false;\r\nApp.mpType = 'app';\r\nconst app = new Vue({\r\n    ...App\r\n});\r\napp.$mount();", "var render, staticRenderFns, recyclableRender, components\nvar renderjs\nimport script from \"./app.js?vue&type=script&lang=js&\"\nexport * from \"./app.js?vue&type=script&lang=js&\"\nimport style0 from \"./app.css?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"App.vue\"\nexport default component.exports"], "sourceRoot": ""}