<view class="demo-page data-v-4f69b280"><responsive-wrapper vue-id="311a3246-1" center="{{true}}" shadow="{{true}}" rounded="{{true}}" max-width="lg" class="data-v-4f69b280" bind:__l="__l" vue-slots="{{['default']}}"><view class="demo-header data-v-4f69b280"><text class="demo-title data-v-4f69b280">响应式布局演示</text><text class="demo-subtitle data-v-4f69b280">Desktop & Mobile Responsive Design</text></view></responsive-wrapper><responsive-wrapper vue-id="311a3246-2" center="{{true}}" shadow="{{true}}" rounded="{{true}}" max-width="lg" custom-class="margin-top" class="data-v-4f69b280" bind:__l="__l" vue-slots="{{['default']}}"><view class="demo-content data-v-4f69b280"><view class="demo-section data-v-4f69b280"><text class="section-title data-v-4f69b280">屏幕信息</text><view class="info-grid data-v-4f69b280"><view class="info-item data-v-4f69b280"><text class="info-label data-v-4f69b280">屏幕宽度:</text><text class="info-value data-v-4f69b280">{{screenInfo.width+"px"}}</text></view><view class="info-item data-v-4f69b280"><text class="info-label data-v-4f69b280">屏幕高度:</text><text class="info-value data-v-4f69b280">{{screenInfo.height+"px"}}</text></view><view class="info-item data-v-4f69b280"><text class="info-label data-v-4f69b280">设备类型:</text><text class="info-value data-v-4f69b280">{{isDesktop?'桌面端':'移动端'}}</text></view></view></view><view class="demo-section data-v-4f69b280"><text class="section-title data-v-4f69b280">响应式网格</text><view class="{{['responsive-grid','data-v-4f69b280',responsiveGridClass]}}"><block wx:for="{{6}}" wx:for-item="n" wx:for-index="__i0__" wx:key="*this"><view class="{{['grid-item','data-v-4f69b280',responsiveCardClass]}}"><text class="grid-text data-v-4f69b280">{{"项目 "+n}}</text></view></block></view></view><view class="demo-section data-v-4f69b280"><text class="section-title data-v-4f69b280">响应式按钮</text><view class="button-group data-v-4f69b280"><button class="{{['demo-button','data-v-4f69b280',responsiveButtonClass]}}">{{''+responsiveButtonText+''}}</button><button class="{{['demo-button','secondary','data-v-4f69b280',responsiveCardClass]}}">响应式悬停效果</button></view></view></view></responsive-wrapper></view>