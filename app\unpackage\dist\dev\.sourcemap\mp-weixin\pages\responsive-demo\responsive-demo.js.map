{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/桌面/thinker/app/pages/responsive-demo/responsive-demo.vue?c8fa", "webpack:///D:/桌面/thinker/app/pages/responsive-demo/responsive-demo.vue?97b2", "webpack:///D:/桌面/thinker/app/pages/responsive-demo/responsive-demo.vue?97db", "webpack:///D:/桌面/thinker/app/pages/responsive-demo/responsive-demo.vue?4118", "uni-app:///pages/responsive-demo/responsive-demo.vue", "webpack:///D:/桌面/thinker/app/pages/responsive-demo/responsive-demo.vue?99bd", "webpack:///D:/桌面/thinker/app/pages/responsive-demo/responsive-demo.vue?dbfb"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "onLoad", "console", "methods"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,uBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwI;AACxI;AACmE;AACL;AACqC;;;AAGnG;AAC8K;AAC9K,gBAAgB,qLAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,sGAAM;AACR,EAAE,+GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;ACHA;AAAA;AAAA;AAAA;AAAmqB,CAAgB,iqBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCuDvrB;EACAC;EACAC;IACA;MACA;IAAA,CACA;EACA;EAEAC;IACAC;IACAA;IACAA;EACA;EAEAC;IACA;EAAA;AAEA;AAAA,2B;;;;;;;;;;;;ACxEA;AAAA;AAAA;AAAA;AAAo+B,CAAgB,87BAAG,EAAC,C;;;;;;;;;;;ACAx/B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/responsive-demo/responsive-demo.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/responsive-demo/responsive-demo.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./responsive-demo.vue?vue&type=template&id=4f69b280&scoped=true&\"\nvar renderjs\nimport script from \"./responsive-demo.vue?vue&type=script&lang=js&\"\nexport * from \"./responsive-demo.vue?vue&type=script&lang=js&\"\nimport style0 from \"./responsive-demo.vue?vue&type=style&index=0&id=4f69b280&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4f69b280\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/responsive-demo/responsive-demo.vue\"\nexport default component.exports", "export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-demo.vue?vue&type=template&id=4f69b280&scoped=true&\"", "var render = function () {}\nvar staticRenderFns = []\nvar recyclableRender\nvar components\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-demo.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-demo.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"demo-page\">\n    <responsive-wrapper :center=\"true\" :shadow=\"true\" :rounded=\"true\" max-width=\"lg\">\n      <view class=\"demo-header\">\n        <text class=\"demo-title\">响应式布局演示</text>\n        <text class=\"demo-subtitle\">Desktop & Mobile Responsive Design</text>\n      </view>\n    </responsive-wrapper>\n    \n    <responsive-wrapper :center=\"true\" :shadow=\"true\" :rounded=\"true\" max-width=\"lg\" custom-class=\"margin-top\">\n      <view class=\"demo-content\">\n        <view class=\"demo-section\">\n          <text class=\"section-title\">屏幕信息</text>\n          <view class=\"info-grid\">\n            <view class=\"info-item\">\n              <text class=\"info-label\">屏幕宽度:</text>\n              <text class=\"info-value\">{{ screenInfo.width }}px</text>\n            </view>\n            <view class=\"info-item\">\n              <text class=\"info-label\">屏幕高度:</text>\n              <text class=\"info-value\">{{ screenInfo.height }}px</text>\n            </view>\n            <view class=\"info-item\">\n              <text class=\"info-label\">设备类型:</text>\n              <text class=\"info-value\">{{ isDesktop ? '桌面端' : '移动端' }}</text>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"demo-section\">\n          <text class=\"section-title\">响应式网格</text>\n          <view class=\"responsive-grid\" :class=\"responsiveGridClass\">\n            <view class=\"grid-item\" v-for=\"n in 6\" :key=\"n\" :class=\"responsiveCardClass\">\n              <text class=\"grid-text\">项目 {{ n }}</text>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"demo-section\">\n          <text class=\"section-title\">响应式按钮</text>\n          <view class=\"button-group\">\n            <button class=\"demo-button\" :class=\"getResponsiveClass('mobile-btn', 'desktop-btn')\">\n              {{ getResponsiveValue('移动端按钮', '桌面端按钮') }}\n            </button>\n            <button class=\"demo-button secondary\" :class=\"responsiveCardClass\">\n              响应式悬停效果\n            </button>\n          </view>\n        </view>\n      </view>\n    </responsive-wrapper>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'ResponsiveDemo',\n  data() {\n    return {\n      // 继承自响应式混入的数据会自动可用\n    };\n  },\n  \n  onLoad() {\n    console.log('响应式演示页面加载');\n    console.log('当前屏幕信息:', this.screenInfo);\n    console.log('是否为桌面端:', this.isDesktop);\n  },\n  \n  methods: {\n    // 可以使用响应式混入提供的方法\n  }\n};\n</script>\n\n<style scoped>\n.demo-page {\n  min-height: 100vh;\n  background-color: #f5f5f5;\n  padding: 20rpx;\n}\n\n.margin-top {\n  margin-top: 20rpx;\n}\n\n.demo-header {\n  padding: 40rpx;\n  text-align: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n}\n\n.demo-title {\n  display: block;\n  font-size: 36rpx;\n  font-weight: bold;\n  margin-bottom: 10rpx;\n}\n\n.demo-subtitle {\n  display: block;\n  font-size: 24rpx;\n  opacity: 0.8;\n}\n\n.demo-content {\n  padding: 40rpx;\n}\n\n.demo-section {\n  margin-bottom: 40rpx;\n}\n\n.section-title {\n  display: block;\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 20rpx;\n  padding-bottom: 10rpx;\n  border-bottom: 2rpx solid #e0e0e0;\n}\n\n.info-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200rpx, 1fr));\n  gap: 20rpx;\n}\n\n.info-item {\n  padding: 20rpx;\n  background-color: #f8f9fa;\n  border-radius: 8rpx;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.info-label {\n  font-size: 24rpx;\n  color: #666;\n  margin-bottom: 8rpx;\n}\n\n.info-value {\n  font-size: 28rpx;\n  font-weight: bold;\n  color: #333;\n}\n\n.responsive-grid {\n  display: grid;\n  gap: 20rpx;\n}\n\n.mobile-flex {\n  display: flex !important;\n  flex-wrap: wrap;\n}\n\n.mobile-flex .grid-item {\n  flex: 1;\n  min-width: 150rpx;\n}\n\n.desktop-grid {\n  grid-template-columns: repeat(3, 1fr);\n}\n\n.grid-item {\n  padding: 30rpx;\n  background-color: #ffffff;\n  border-radius: 8rpx;\n  text-align: center;\n  border: 2rpx solid #e0e0e0;\n  transition: all 0.3s ease;\n}\n\n.grid-text {\n  font-size: 28rpx;\n  color: #333;\n}\n\n.button-group {\n  display: flex;\n  gap: 20rpx;\n  flex-wrap: wrap;\n}\n\n.demo-button {\n  padding: 20rpx 40rpx;\n  border: none;\n  border-radius: 8rpx;\n  background-color: #007bff;\n  color: white;\n  font-size: 28rpx;\n  transition: all 0.3s ease;\n}\n\n.demo-button.secondary {\n  background-color: #6c757d;\n}\n\n.mobile-btn {\n  font-size: 26rpx;\n  padding: 15rpx 30rpx;\n}\n\n.desktop-btn {\n  font-size: 32rpx;\n  padding: 25rpx 50rpx;\n}\n\n/* 桌面端样式 */\n@media screen and (min-width: 1024px) {\n  .demo-page {\n    padding: 40px;\n  }\n  \n  .demo-header {\n    padding: 60px;\n    border-radius: 12px 12px 0 0;\n  }\n  \n  .demo-title {\n    font-size: 28px;\n    margin-bottom: 8px;\n  }\n  \n  .demo-subtitle {\n    font-size: 16px;\n  }\n  \n  .demo-content {\n    padding: 60px;\n  }\n  \n  .section-title {\n    font-size: 24px;\n    margin-bottom: 16px;\n    padding-bottom: 8px;\n  }\n  \n  .info-grid {\n    grid-template-columns: repeat(3, 1fr);\n    gap: 20px;\n  }\n  \n  .info-item {\n    padding: 20px;\n    border-radius: 8px;\n  }\n  \n  .info-label {\n    font-size: 14px;\n    margin-bottom: 6px;\n  }\n  \n  .info-value {\n    font-size: 18px;\n  }\n  \n  .responsive-grid {\n    gap: 20px;\n  }\n  \n  .desktop-grid {\n    grid-template-columns: repeat(4, 1fr);\n  }\n  \n  .grid-item {\n    padding: 30px;\n    border-radius: 12px;\n  }\n  \n  .grid-item:hover {\n    transform: translateY(-4px);\n    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\n    border-color: #007bff;\n  }\n  \n  .grid-text {\n    font-size: 16px;\n  }\n  \n  .demo-button {\n    padding: 15px 30px;\n    border-radius: 8px;\n    font-size: 16px;\n  }\n  \n  .demo-button:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);\n  }\n  \n  .demo-button.secondary:hover {\n    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);\n  }\n}\n</style>\n", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-demo.vue?vue&type=style&index=0&id=4f69b280&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-demo.vue?vue&type=style&index=0&id=4f69b280&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753561620938\n      var cssReload = require(\"D:/uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}