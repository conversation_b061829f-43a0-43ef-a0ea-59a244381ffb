<view class="main-container"><block wx:if="{{load}}"><view><responsive-wrapper vue-id="380011e0-1" center="{{true}}" shadow="{{false}}" rounded="{{false}}" max-width="lg" bg-color="transparent" bind:__l="__l" vue-slots="{{['default']}}"><view class="cu-avatar xl round" style="{{('background-image:url('+user.avatar+');')}}"><block wx:if="{{user.is_vip==1}}"><view class="cu-tag badge bg-yellow cuIcon-vip"></view></block></view><view class="{{['text-xl margin-top',cssData.textContent]}}"><text>{{"你好，"+user.nickname}}</text></view><view class="{{['margin-top-sm',cssData.textContent]}}"><text class="sign">相信美好的事情即将发生</text></view><image class="gif-wave" mode="scaleToFill" src="https://learnfile.20230611.cn/learnAppClient/cb/de/cbde6f5e83388a096d28fab7339d56fd.gif"></image></responsive-wrapper></view></block><view class="padding flex text-center text-grey bg-white shadow-warp"><view data-event-opts="{{[['tap',[['copyTap',['$0'],['user.id']]]]]}}" class="flex flex-sub flex-direction solid-right" bindtap="__e"><view class="{{['text-xxl text-orange',cssData.textContent]}}">{{user.id}}</view><view class="margin-top-sm"><text class="cuIcon-cuIcon"></text>编号</view></view><view class="flex flex-sub flex-direction solid-right" data-url="{{paths.rechargeMember}}" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><view class="{{['text-xxl text-blue',cssData.textContent]}}">{{user.vip_name}}</view><view class="margin-top-sm"><text class="cuIcon-peoplefill"></text>级别</view></view><view class="flex flex-sub flex-direction" data-url="{{paths.score}}" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><view class="{{['text-xxl text-green',cssData.textContent]}}">{{user.score}}</view><view class="margin-top-sm"><text class="cuIcon-rechargefill"></text>积分</view></view></view><view class="cu-bar bg-white solid-bottom margin-top-xs"><view class="action"><text class="text-orange"></text>账户管理</view></view><view class="cu-list grid col-5 no-border"><block wx:if="{{!user.openid}}"><view class="{{['cu-item',cssData.menuItem]}}" data-url="{{paths.wechat}}" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><image class="menu-image" style="width:100%;" mode="aspectFit" src="/static/img/wechat.png"></image><text>绑定微信</text></view></block><block wx:if="{{!appIsAudit&&!user.email}}"><view class="{{['cu-item',cssData.menuItem]}}" data-url="{{paths.email}}" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><image class="menu-image" style="width:100%;" mode="aspectFit" src="/static/img/email.png"></image><text>绑定邮箱</text></view></block><block wx:if="{{!appIsAudit&&!user.mobile}}"><view class="{{['cu-item',cssData.menuItem]}}" data-url="{{paths.mobile}}" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><image class="menu-image" style="width:100%;" mode="aspectFit" src="/static/img/phone.png"></image><text>绑定手机</text></view></block><view class="{{['cu-item',cssData.menuItem]}}" data-url="{{paths.password}}" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><image class="menu-image" style="width:100%;" mode="aspectFit" src="/static/img/password.png"></image><text>设置密码</text></view><view class="cu-item" data-url="{{paths.info}}" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><image class="menu-image" style="width:100%;" mode="aspectFit" src="/static/img/user-male-circle.png"></image><text>个人资料</text></view></view><view class="cu-bar bg-white solid-bottom margin-top-xs"><view class="action"><text class="text-orange"></text>学习管理</view></view><view class="cu-list grid col-5 no-border"><view class="{{['cu-item',cssData.menuItem]}}" data-url="{{paths.myCourse}}" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><image class="menu-image" style="width:100%;" mode="aspectFit" src="/static/img/book.png"></image><text>我的题库</text></view><view class="{{['cu-item',cssData.menuItem]}}" data-url="{{paths.myErrorQuestion}}" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><image class="menu-image" style="width:100%;" mode="aspectFit" src="/static/img/error.png"></image><text>我的错题</text></view><view class="{{['cu-item',cssData.menuItem]}}" data-url="{{paths.myCollect}}" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><image class="menu-image" style="width:100%;" mode="aspectFit" src="/static/img/bookmark.png"></image><text>我的收藏</text></view></view><view class="cu-bar bg-white solid-bottom margin-top-xs"><view class="action"><text class="text-orange"></text>我的服务</view></view><view class="cu-list grid col-5 no-border"><block wx:if="{{!appIsAudit}}"><view class="{{['cu-item',cssData.menuItem]}}" data-url="{{paths.about}}" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><image class="menu-image" style="width:100%;" mode="aspectFit" src="/static/img/about.png"></image><text>关于我们</text></view></block><block wx:if="{{appPlatform==20||appPlatform==21}}"><view data-event-opts="{{[['tap',[['showOnlineServiceTap']]]]}}" class="{{['cu-item',cssData.menuItem]}}" bindtap="__e"><image class="menu-image" style="width:100%;" mode="aspectFit" src="/static/img/customer-support.png"></image><text>在线客服</text></view></block><view class="{{['cu-item',cssData.menuItem]}}" data-url="{{paths.opinion}}" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><image class="menu-image" style="width:100%;" mode="aspectFit" src="/static/img/feeback.png"></image><text>意见反馈</text></view><block wx:if="{{user.is_agent==1}}"><view class="cu-item" data-url="{{paths.promote}}" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><image class="menu-image" style="width:100%;" mode="aspectFit" src="/static/img/share.png"></image><text>推广中心</text></view></block></view></view>