@import 'common/colorui/adaptation.css';
@import 'common/colorui/main.css';
@import 'common/colorui/icon.css';
@import 'common/font-awesome/style.css';
@import 'common/css/responsive.css';

page {
	background-color: #f5f5f5;
}
uni-toast {
	z-index: 10000;
}

/* 微信小程序电脑端大屏响应式布局 */
/* 针对电脑端微信小程序的响应式优化 */

/* 基础容器样式 - 电脑端居中显示 */
@media screen and (min-width: 1024px) {
	page {
		background-color: #f0f2f5;
		padding: 0;
	}

	/* 主容器居中布局 */
	.main-container {
		max-width: 1200px;
		margin: 0 auto;
		background-color: #ffffff;
		box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
		min-height: 100vh;
		position: relative;
	}

	/* 顶部导航栏适配 */
	.cu-bar {
		max-width: 1200px;
		margin: 0 auto;
		position: relative;
	}

	.cu-bar.fixed {
		position: fixed;
		left: 50%;
		transform: translateX(-50%);
		max-width: 1200px;
		width: 100%;
		z-index: 1024;
	}

	/* 搜索栏优化 */
	.cu-bar.search {
		padding: 20px 40px;
		justify-content: center;
		gap: 40px;
	}

	.cu-bar.search .action {
		font-size: 16px !important;
		max-width: none !important;
		flex: 0 0 auto;
		padding: 8px 16px;
		border-radius: 20px;
		background-color: #f8f9fa;
		transition: all 0.3s ease;
	}

	.cu-bar.search .action:hover {
		background-color: #e9ecef;
		transform: translateY(-1px);
	}
}

/* 大屏幕优化 (1440px+) */
@media screen and (min-width: 1440px) {
	.main-container {
		max-width: 1400px;
	}

	.cu-bar.fixed {
		max-width: 1400px;
	}

	.cu-bar.search {
		padding: 25px 50px;
		gap: 50px;
	}
}

/* 轮播图响应式优化 */
@media screen and (min-width: 1024px) {
	.screen-swiper {
		max-width: 1200px;
		margin: 20px auto;
		border-radius: 12px;
		overflow: hidden;
		box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
	}

	.screen-swiper image {
		height: 300px;
		object-fit: cover;
	}
}

@media screen and (min-width: 1440px) {
	.screen-swiper {
		max-width: 1400px;
		margin: 30px auto;
	}

	.screen-swiper image {
		height: 350px;
	}
}

/* 学习中心网格布局响应式优化 */
@media screen and (min-width: 1024px) {
	.modern-grid {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		gap: 30px;
		padding: 40px;
		max-width: 1200px;
		margin: 0 auto;
	}

	.modern-grid-item {
		width: 100%;
		padding: 30px 20px;
		background-color: #ffffff;
		border-radius: 12px;
		box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
		transition: all 0.3s ease;
		border: 1px solid #f0f0f0;
	}

	.modern-grid-item:hover {
		transform: translateY(-4px);
		box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
		border-color: #0081ff;
	}

	.modern-grid-icon {
		width: 48px;
		height: 48px;
		margin-bottom: 16px;
	}

	.modern-grid-text {
		font-size: 16px;
		color: #333;
		font-weight: 500;
	}
}

@media screen and (min-width: 1440px) {
	.modern-grid {
		grid-template-columns: repeat(5, 1fr);
		gap: 40px;
		padding: 50px;
		max-width: 1400px;
	}

	.modern-grid-item {
		padding: 35px 25px;
	}

	.modern-grid-icon {
		width: 56px;
		height: 56px;
		margin-bottom: 20px;
	}

	.modern-grid-text {
		font-size: 18px;
	}
}

/* 内容区域响应式优化 */
@media screen and (min-width: 1024px) {
	.cu-bar.bg-white {
		max-width: 1200px;
		margin: 20px auto 0;
		border-radius: 12px;
		box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
		padding: 20px 40px;
	}

	.cu-bar .action.sub-title {
		flex-direction: column;
		align-items: flex-start;
	}

	.cu-bar .action.sub-title .text-xl {
		font-size: 24px;
		margin-bottom: 4px;
	}

	.cu-bar .action.sub-title .text-ABC {
		font-size: 14px;
		opacity: 0.7;
	}

	/* 分享按钮优化 */
	.cu-btn.bg-blue {
		padding: 12px 24px;
		font-size: 16px;
		border-radius: 8px;
		transition: all 0.3s ease;
	}

	.cu-btn.bg-blue:hover {
		transform: translateY(-2px);
		box-shadow: 0 4px 15px rgba(0, 129, 255, 0.3);
	}
}

@media screen and (min-width: 1440px) {
	.cu-bar.bg-white {
		max-width: 1400px;
		padding: 25px 50px;
	}

	.cu-bar .action.sub-title .text-xl {
		font-size: 28px;
	}

	.cu-bar .action.sub-title .text-ABC {
		font-size: 16px;
	}
}

/* 文章列表响应式优化 */
@media screen and (min-width: 1024px) {
	.cu-card.article {
		max-width: 1200px;
		margin: 0 auto;
	}

	.cu-card.article .cu-item {
		margin: 0 20px 20px;
		border-radius: 12px;
		box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
		transition: all 0.3s ease;
		border: 1px solid #f0f0f0;
	}

	.cu-card.article .cu-item:hover {
		transform: translateY(-2px);
		box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
	}

	.cu-card.article .cu-item .content {
		padding: 25px 30px;
		display: flex;
		align-items: center;
		gap: 20px;
	}

	.cu-card.article .cu-item .content image {
		width: 120px;
		height: 90px;
		border-radius: 8px;
		object-fit: cover;
		flex-shrink: 0;
	}

	.cu-card.article .cu-item .desc {
		flex: 1;
		display: flex;
		flex-direction: column;
		gap: 12px;
	}

	.cu-card.article .cu-item .desc .text-black {
		font-size: 18px;
		line-height: 1.4;
		font-weight: 500;
		color: #333;
	}

	.cu-card.article .cu-item .desc .flex {
		align-items: center;
	}

	.cu-card.article .cu-item .desc .text-gray {
		font-size: 14px;
		color: #666;
	}
}

@media screen and (min-width: 1440px) {
	.cu-card.article {
		max-width: 1400px;
	}

	.cu-card.article .cu-item {
		margin: 0 30px 25px;
	}

	.cu-card.article .cu-item .content {
		padding: 30px 40px;
		gap: 25px;
	}

	.cu-card.article .cu-item .content image {
		width: 140px;
		height: 105px;
	}

	.cu-card.article .cu-item .desc .text-black {
		font-size: 20px;
	}

	.cu-card.article .cu-item .desc .text-gray {
		font-size: 16px;
	}
}

/* 底部导航栏响应式优化 */
@media screen and (min-width: 1024px) {
	.cu-bar.tabbar {
		max-width: 1200px;
		left: 50%;
		transform: translateX(-50%);
		border-radius: 20px 20px 0 0;
		box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
		border: 1px solid #f0f0f0;
		border-bottom: none;
	}

	.cu-bar.tabbar .action {
		font-size: 16px;
		padding: 15px 10px;
	}

	.cu-bar.tabbar .action [class*="cuIcon-"] {
		font-size: 28px;
		margin-bottom: 8px;
	}
}

@media screen and (min-width: 1440px) {
	.cu-bar.tabbar {
		max-width: 1400px;
	}

	.cu-bar.tabbar .action {
		font-size: 18px;
		padding: 18px 15px;
	}

	.cu-bar.tabbar .action [class*="cuIcon-"] {
		font-size: 32px;
		margin-bottom: 10px;
	}
}

/* 通用响应式工具类 */
@media screen and (min-width: 1024px) {
	/* 间距工具类 */
	.desktop-p-lg { padding: 40px !important; }
	.desktop-p-xl { padding: 50px !important; }
	.desktop-m-lg { margin: 40px !important; }
	.desktop-m-xl { margin: 50px !important; }

	/* 文字大小工具类 */
	.desktop-text-sm { font-size: 14px !important; }
	.desktop-text-md { font-size: 16px !important; }
	.desktop-text-lg { font-size: 18px !important; }
	.desktop-text-xl { font-size: 20px !important; }
	.desktop-text-2xl { font-size: 24px !important; }

	/* 显示/隐藏工具类 */
	.desktop-show { display: block !important; }
	.desktop-hide { display: none !important; }
	.mobile-hide { display: none !important; }

	/* 布局工具类 */
	.desktop-center {
		max-width: 1200px;
		margin: 0 auto;
	}

	.desktop-flex {
		display: flex !important;
		gap: 20px;
	}

	.desktop-grid {
		display: grid !important;
		gap: 20px;
	}

	/* 圆角和阴影 */
	.desktop-rounded {
		border-radius: 12px !important;
	}

	.desktop-shadow {
		box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
	}

	.desktop-shadow-hover:hover {
		box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15) !important;
		transform: translateY(-2px);
	}
}

/* 移动端样式保护 - 确保移动端样式不受影响 */
@media screen and (max-width: 1023px) {
	.main-container {
		max-width: none !important;
		margin: 0 !important;
		box-shadow: none !important;
	}

	.cu-bar.fixed {
		position: fixed !important;
		left: 0 !important;
		transform: none !important;
		max-width: none !important;
		width: 100% !important;
	}

	.modern-grid {
		display: flex !important;
		flex-wrap: wrap !important;
		grid-template-columns: none !important;
		gap: 0 !important;
	}

	.modern-grid-item {
		width: 33.33% !important;
		box-shadow: none !important;
		border: none !important;
		background-color: transparent !important;
	}

	.cu-card.article .cu-item {
		margin: 0 !important;
		box-shadow: none !important;
		border: none !important;
		border-radius: 0 !important;
	}

	.cu-bar.tabbar {
		left: 0 !important;
		transform: none !important;
		max-width: none !important;
		border-radius: 0 !important;
		border: none !important;
	}

	/* 移动端显示/隐藏 */
	.mobile-show { display: block !important; }
	.desktop-hide-mobile { display: none !important; }
}