.menu-image {
	width: 56rpx;
	height: 56rpx;
	margin-bottom: 10rpx;
}


/* 考试倒计时样式 */
.exam-countdown {
	padding: 0;
	border-radius: 12rpx;
	margin: 0 20rpx;
	overflow: hidden;
}
.countdown-content {
	background: linear-gradient(135deg, #f0f8ff, #e6f2ff);
	border-radius: 12rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 100, 255, 0.15);
	padding: 0;
	position: relative;
	overflow: hidden;
}
.countdown-header {
	background-color: #0081ff;
	color: #ffffff;
	padding: 16rpx 24rpx;
	display: flex;
	align-items: center;
}
.countdown-title {
	font-size: 28rpx;
	font-weight: 500;
	margin-left: 10rpx;
}
.countdown-info {
	padding: 20rpx 24rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.countdown-date {
	font-size: 26rpx;
	color: #555;
}
.countdown-timer {
	display: flex;
	align-items: center;
}
.countdown-days {
	font-size: 40rpx;
	font-weight: bold;
	color: #0081ff;
	background-color: rgba(0, 129, 255, 0.1);
	padding: 6rpx 16rpx;
	border-radius: 8rpx;
	margin-right: 8rpx;
}
.countdown-unit {
	font-size: 28rpx;
	color: #555;
}


/* 新的宫格列表样式 */
.modern-grid {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	padding: 20rpx 10rpx;
}
.modern-grid-item {
	width: 33.33%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 20rpx 0;
	transition: all 0.3s ease;
}
.modern-grid-item:active {
	-webkit-transform: scale(0.95);
	        transform: scale(0.95);
	opacity: 0.8;
}
.modern-grid-icon {
	width: 60rpx;
	height: 60rpx;
	margin-bottom: 10rpx;
	border-radius: 12rpx;
}
.modern-grid-text {
	font-size: 26rpx;
	color: #333;
	font-weight: 500;
}


/* 电脑端响应式优化 */
@media screen and (min-width: 1024px) {
	/* 考试倒计时优化 */
.exam-countdown {
		max-width: 1200px;
		margin: 20px auto;
		border-radius: 12px;
		box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}
.countdown-content {
		border-radius: 12px;
}
.countdown-header {
		padding: 20px 30px;
		font-size: 18px;
}
.countdown-info {
		padding: 25px 30px;
}
.countdown-date {
		font-size: 16px;
}
.countdown-days {
		font-size: 28px;
		padding: 8px 20px;
		border-radius: 8px;
		margin: 0 10px;
}
.countdown-unit {
		font-size: 16px;
}

	/* 轮播图优化 */
.screen-swiper {
		max-width: 1200px;
		margin: 20px auto;
		border-radius: 12px;
		overflow: hidden;
		box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}
.swiper-image {
		height: 300px;
		object-fit: cover;
		width: 100%;
}
}
@media screen and (min-width: 1440px) {
.exam-countdown {
		max-width: 1400px;
		margin: 30px auto;
}
.countdown-header {
		padding: 25px 40px;
		font-size: 20px;
}
.countdown-info {
		padding: 30px 40px;
}
.countdown-date {
		font-size: 18px;
}
.countdown-days {
		font-size: 32px;
		padding: 10px 24px;
}
.countdown-unit {
		font-size: 18px;
}
.screen-swiper {
		max-width: 1400px;
		margin: 30px auto;
}
.swiper-image {
		height: 350px;
}
}

