
.demo-page.data-v-4f69b280 {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}
.margin-top.data-v-4f69b280 {
  margin-top: 20rpx;
}
.demo-header.data-v-4f69b280 {
  padding: 40rpx;
  text-align: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}
.demo-title.data-v-4f69b280 {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}
.demo-subtitle.data-v-4f69b280 {
  display: block;
  font-size: 24rpx;
  opacity: 0.8;
}
.demo-content.data-v-4f69b280 {
  padding: 40rpx;
}
.demo-section.data-v-4f69b280 {
  margin-bottom: 40rpx;
}
.section-title.data-v-4f69b280 {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 2rpx solid #e0e0e0;
}
.info-grid.data-v-4f69b280 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200rpx, 1fr));
  gap: 20rpx;
}
.info-item.data-v-4f69b280 {
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.info-label.data-v-4f69b280 {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}
.info-value.data-v-4f69b280 {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}
.responsive-grid.data-v-4f69b280 {
  display: grid;
  gap: 20rpx;
}
.mobile-flex.data-v-4f69b280 {
  display: flex !important;
  flex-wrap: wrap;
}
.mobile-flex .grid-item.data-v-4f69b280 {
  flex: 1;
  min-width: 150rpx;
}
.desktop-grid.data-v-4f69b280 {
  grid-template-columns: repeat(3, 1fr);
}
.grid-item.data-v-4f69b280 {
  padding: 30rpx;
  background-color: #ffffff;
  border-radius: 8rpx;
  text-align: center;
  border: 2rpx solid #e0e0e0;
  transition: all 0.3s ease;
}
.grid-text.data-v-4f69b280 {
  font-size: 28rpx;
  color: #333;
}
.button-group.data-v-4f69b280 {
  display: flex;
  gap: 20rpx;
  flex-wrap: wrap;
}
.demo-button.data-v-4f69b280 {
  padding: 20rpx 40rpx;
  border: none;
  border-radius: 8rpx;
  background-color: #007bff;
  color: white;
  font-size: 28rpx;
  transition: all 0.3s ease;
}
.demo-button.secondary.data-v-4f69b280 {
  background-color: #6c757d;
}
.mobile-btn.data-v-4f69b280 {
  font-size: 26rpx;
  padding: 15rpx 30rpx;
}
.desktop-btn.data-v-4f69b280 {
  font-size: 32rpx;
  padding: 25rpx 50rpx;
}

/* 桌面端样式 */
@media screen and (min-width: 1024px) {
.demo-page.data-v-4f69b280 {
    padding: 40px;
}
.demo-header.data-v-4f69b280 {
    padding: 60px;
    border-radius: 12px 12px 0 0;
}
.demo-title.data-v-4f69b280 {
    font-size: 28px;
    margin-bottom: 8px;
}
.demo-subtitle.data-v-4f69b280 {
    font-size: 16px;
}
.demo-content.data-v-4f69b280 {
    padding: 60px;
}
.section-title.data-v-4f69b280 {
    font-size: 24px;
    margin-bottom: 16px;
    padding-bottom: 8px;
}
.info-grid.data-v-4f69b280 {
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}
.info-item.data-v-4f69b280 {
    padding: 20px;
    border-radius: 8px;
}
.info-label.data-v-4f69b280 {
    font-size: 14px;
    margin-bottom: 6px;
}
.info-value.data-v-4f69b280 {
    font-size: 18px;
}
.responsive-grid.data-v-4f69b280 {
    gap: 20px;
}
.desktop-grid.data-v-4f69b280 {
    grid-template-columns: repeat(4, 1fr);
}
.grid-item.data-v-4f69b280 {
    padding: 30px;
    border-radius: 12px;
}
.grid-item.data-v-4f69b280:hover {
    -webkit-transform: translateY(-4px);
            transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: #007bff;
}
.grid-text.data-v-4f69b280 {
    font-size: 16px;
}
.demo-button.data-v-4f69b280 {
    padding: 15px 30px;
    border-radius: 8px;
    font-size: 16px;
}
.demo-button.data-v-4f69b280:hover {
    -webkit-transform: translateY(-2px);
            transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}
.demo-button.secondary.data-v-4f69b280:hover {
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}
}

