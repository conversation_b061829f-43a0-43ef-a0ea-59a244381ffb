<template>
  <view class="responsive-wrapper" :class="wrapperClass">
    <slot></slot>
  </view>
</template>

<script>
export default {
  name: 'ResponsiveWrapper',
  props: {
    // 是否启用桌面端居中布局
    center: {
      type: Boolean,
      default: true
    },
    // 最大宽度类型
    maxWidth: {
      type: String,
      default: 'lg', // xs, sm, md, lg, xl
      validator: value => ['xs', 'sm', 'md', 'lg', 'xl'].includes(value)
    },
    // 是否添加阴影
    shadow: {
      type: Boolean,
      default: false
    },
    // 是否添加圆角
    rounded: {
      type: Boolean,
      default: false
    },
    // 背景色
    bgColor: {
      type: String,
      default: 'white'
    },
    // 自定义类名
    customClass: {
      type: String,
      default: ''
    }
  },
  computed: {
    wrapperClass() {
      const classes = [];
      
      if (this.center) {
        classes.push('responsive-center');
      }
      
      classes.push(`max-width-${this.maxWidth}`);
      
      if (this.shadow) {
        classes.push('desktop-shadow');
      }
      
      if (this.rounded) {
        classes.push('desktop-rounded');
      }
      
      classes.push(`bg-${this.bgColor}`);
      
      if (this.customClass) {
        classes.push(this.customClass);
      }
      
      return classes.join(' ');
    }
  }
};
</script>

<style scoped>
.responsive-wrapper {
  width: 100%;
  box-sizing: border-box;
}

/* 移动端样式 */
@media screen and (max-width: 1023px) {
  .responsive-wrapper {
    margin: 0;
    max-width: none !important;
    box-shadow: none !important;
    border-radius: 0 !important;
  }
}

/* 桌面端样式 */
@media screen and (min-width: 1024px) {
  .responsive-center {
    margin: 0 auto;
  }
  
  .max-width-xs {
    max-width: 600px;
  }
  
  .max-width-sm {
    max-width: 800px;
  }
  
  .max-width-md {
    max-width: 1000px;
  }
  
  .max-width-lg {
    max-width: 1200px;
  }
  
  .max-width-xl {
    max-width: 1400px;
  }
  
  .desktop-shadow {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }
  
  .desktop-rounded {
    border-radius: 12px;
  }
  
  .bg-white {
    background-color: #ffffff;
  }
  
  .bg-transparent {
    background-color: transparent;
  }
}

@media screen and (min-width: 1440px) {
  .max-width-lg {
    max-width: 1400px;
  }
  
  .max-width-xl {
    max-width: 1600px;
  }
}
</style>
