{"version": 3, "sources": ["webpack:///D:/桌面/thinker/app/components/responsive-wrapper/responsive-wrapper.vue?9a99", "webpack:///D:/桌面/thinker/app/components/responsive-wrapper/responsive-wrapper.vue?d020", "webpack:///D:/桌面/thinker/app/components/responsive-wrapper/responsive-wrapper.vue?6f92", "webpack:///D:/桌面/thinker/app/components/responsive-wrapper/responsive-wrapper.vue?398d", "uni-app:///components/responsive-wrapper/responsive-wrapper.vue", "webpack:///D:/桌面/thinker/app/components/responsive-wrapper/responsive-wrapper.vue?7a38", "webpack:///D:/桌面/thinker/app/components/responsive-wrapper/responsive-wrapper.vue?0e46"], "names": ["name", "props", "center", "type", "default", "max<PERSON><PERSON><PERSON>", "validator", "shadow", "rounded", "bgColor", "customClass", "computed", "wrapperClass", "classes"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA2I;AAC3I;AACsE;AACL;AACqC;;;AAGtG;AAC8K;AAC9K,gBAAgB,qLAAU;AAC1B,EAAE,wFAAM;AACR,EAAE,yGAAM;AACR,EAAE,kHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,6GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAsqB,CAAgB,oqBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;eCO1rB;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;MAAA;MACAE;QAAA;MAAA;IACA;IACA;IACAC;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;EACA;EACAO;IACAC;MACA;MAEA;QACAC;MACA;MAEAA;MAEA;QACAA;MACA;MAEA;QACAA;MACA;MAEAA;MAEA;QACAA;MACA;MAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACrEA;AAAA;AAAA;AAAA;AAAu+B,CAAgB,i8BAAG,EAAC,C;;;;;;;;;;;ACA3/B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/responsive-wrapper/responsive-wrapper.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./responsive-wrapper.vue?vue&type=template&id=4f792484&scoped=true&\"\nvar renderjs\nimport script from \"./responsive-wrapper.vue?vue&type=script&lang=js&\"\nexport * from \"./responsive-wrapper.vue?vue&type=script&lang=js&\"\nimport style0 from \"./responsive-wrapper.vue?vue&type=style&index=0&id=4f792484&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4f792484\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/responsive-wrapper/responsive-wrapper.vue\"\nexport default component.exports", "export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-wrapper.vue?vue&type=template&id=4f792484&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-wrapper.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-wrapper.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"responsive-wrapper\" :class=\"wrapperClass\">\n    <slot></slot>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'ResponsiveWrapper',\n  props: {\n    // 是否启用桌面端居中布局\n    center: {\n      type: Boolean,\n      default: true\n    },\n    // 最大宽度类型\n    maxWidth: {\n      type: String,\n      default: 'lg', // xs, sm, md, lg, xl\n      validator: value => ['xs', 'sm', 'md', 'lg', 'xl'].includes(value)\n    },\n    // 是否添加阴影\n    shadow: {\n      type: Boolean,\n      default: false\n    },\n    // 是否添加圆角\n    rounded: {\n      type: Boolean,\n      default: false\n    },\n    // 背景色\n    bgColor: {\n      type: String,\n      default: 'white'\n    },\n    // 自定义类名\n    customClass: {\n      type: String,\n      default: ''\n    }\n  },\n  computed: {\n    wrapperClass() {\n      const classes = [];\n      \n      if (this.center) {\n        classes.push('responsive-center');\n      }\n      \n      classes.push(`max-width-${this.maxWidth}`);\n      \n      if (this.shadow) {\n        classes.push('desktop-shadow');\n      }\n      \n      if (this.rounded) {\n        classes.push('desktop-rounded');\n      }\n      \n      classes.push(`bg-${this.bgColor}`);\n      \n      if (this.customClass) {\n        classes.push(this.customClass);\n      }\n      \n      return classes.join(' ');\n    }\n  }\n};\n</script>\n\n<style scoped>\n.responsive-wrapper {\n  width: 100%;\n  box-sizing: border-box;\n}\n\n/* 移动端样式 */\n@media screen and (max-width: 1023px) {\n  .responsive-wrapper {\n    margin: 0;\n    max-width: none !important;\n    box-shadow: none !important;\n    border-radius: 0 !important;\n  }\n}\n\n/* 桌面端样式 */\n@media screen and (min-width: 1024px) {\n  .responsive-center {\n    margin: 0 auto;\n  }\n  \n  .max-width-xs {\n    max-width: 600px;\n  }\n  \n  .max-width-sm {\n    max-width: 800px;\n  }\n  \n  .max-width-md {\n    max-width: 1000px;\n  }\n  \n  .max-width-lg {\n    max-width: 1200px;\n  }\n  \n  .max-width-xl {\n    max-width: 1400px;\n  }\n  \n  .desktop-shadow {\n    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n  }\n  \n  .desktop-rounded {\n    border-radius: 12px;\n  }\n  \n  .bg-white {\n    background-color: #ffffff;\n  }\n  \n  .bg-transparent {\n    background-color: transparent;\n  }\n}\n\n@media screen and (min-width: 1440px) {\n  .max-width-lg {\n    max-width: 1400px;\n  }\n  \n  .max-width-xl {\n    max-width: 1600px;\n  }\n}\n</style>\n", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-wrapper.vue?vue&type=style&index=0&id=4f792484&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-wrapper.vue?vue&type=style&index=0&id=4f792484&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753562137442\n      var cssReload = require(\"D:/uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}