import App from './App.vue';
// Api函数polyfill（目前为实验版本，如不需要，可删除！）';
import Polyfill from './polyfill/polyfill.js';
Polyfill.init();
// 全局mixins，用于实现setData等功能，请勿删除！';
import Mixin from './polyfill/mixins.js';
// 响应式设计混入
import ResponsiveMixin from './mixins/responsive.js';
// 响应式包装组件
import ResponsiveWrapper from './components/responsive-wrapper/responsive-wrapper.vue';

// #ifndef VUE3
import Vue from 'vue';

Vue.mixin(Mixin);
Vue.mixin(ResponsiveMixin);
Vue.component('ResponsiveWrapper', ResponsiveWrapper);
Vue.config.productionTip = false;
App.mpType = 'app';
const app = new Vue({
    ...App
});
app.$mount();
// #endif